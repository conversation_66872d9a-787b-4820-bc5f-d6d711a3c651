"""
Django management command to optimize database performance
Usage: python manage.py optimize_database
"""

import os
import sys
from django.core.management.base import BaseCommand
from django.core.management import call_command
from django.db import connection
from django.conf import settings


class Command(BaseCommand):
    help = 'Optimize database performance with indexes and settings'

    def add_arguments(self, parser):
        parser.add_argument(
            '--skip-migrations',
            action='store_true',
            help='Skip running migrations',
        )
        parser.add_argument(
            '--analyze-only',
            action='store_true',
            help='Only analyze current performance, do not make changes',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('🚀 Starting Database Performance Optimization')
        )
        
        if options['analyze_only']:
            self.analyze_performance()
            return
        
        # Step 1: Create and run migrations for new indexes
        if not options['skip_migrations']:
            self.create_migrations()
            self.run_migrations()
        
        # Step 2: Optimize SQLite settings
        self.optimize_sqlite()
        
        # Step 3: Analyze tables
        self.analyze_tables()
        
        # Step 4: Show performance tips
        self.show_performance_tips()
        
        self.stdout.write(
            self.style.SUCCESS('✅ Database optimization completed!')
        )

    def create_migrations(self):
        """Create migrations for model changes"""
        self.stdout.write('📝 Creating migrations for database indexes...')
        
        try:
            call_command('makemigrations', 'pos', verbosity=1)
            self.stdout.write(self.style.SUCCESS('  ✅ Migrations created'))
        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f'  ⚠️  Migration creation: {e}')
            )

    def run_migrations(self):
        """Run pending migrations"""
        self.stdout.write('🔄 Running database migrations...')
        
        try:
            call_command('migrate', verbosity=1)
            self.stdout.write(self.style.SUCCESS('  ✅ Migrations applied'))
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'  ❌ Migration error: {e}')
            )

    def optimize_sqlite(self):
        """Apply SQLite-specific optimizations"""
        self.stdout.write('⚡ Applying SQLite optimizations...')
        
        try:
            with connection.cursor() as cursor:
                # Enable WAL mode for better concurrency
                cursor.execute("PRAGMA journal_mode=WAL;")
                
                # Optimize synchronous mode
                cursor.execute("PRAGMA synchronous=NORMAL;")
                
                # Increase cache size
                cursor.execute("PRAGMA cache_size=10000;")
                
                # Use memory for temporary storage
                cursor.execute("PRAGMA temp_store=MEMORY;")
                
                # Enable memory mapping
                cursor.execute("PRAGMA mmap_size=268435456;")  # 256MB
                
                # Optimize page size
                cursor.execute("PRAGMA page_size=4096;")
                
                self.stdout.write(self.style.SUCCESS('  ✅ SQLite optimizations applied'))
                
        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f'  ⚠️  SQLite optimization: {e}')
            )

    def analyze_tables(self):
        """Analyze tables for query optimization"""
        self.stdout.write('📊 Analyzing database tables...')
        
        try:
            with connection.cursor() as cursor:
                # Analyze all tables to update statistics
                cursor.execute("ANALYZE;")
                
                # Vacuum to reclaim space and optimize
                cursor.execute("VACUUM;")
                
                self.stdout.write(self.style.SUCCESS('  ✅ Database analysis completed'))
                
        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f'  ⚠️  Table analysis: {e}')
            )

    def analyze_performance(self):
        """Analyze current database performance"""
        self.stdout.write('🔍 Analyzing current database performance...')
        
        try:
            with connection.cursor() as cursor:
                # Check current PRAGMA settings
                pragmas = [
                    'journal_mode', 'synchronous', 'cache_size', 
                    'temp_store', 'mmap_size', 'page_size'
                ]
                
                self.stdout.write('\n📋 Current SQLite Settings:')
                for pragma in pragmas:
                    cursor.execute(f"PRAGMA {pragma};")
                    result = cursor.fetchone()
                    self.stdout.write(f'  {pragma}: {result[0] if result else "N/A"}')
                
                # Check table sizes
                cursor.execute("""
                    SELECT name
                    FROM sqlite_master
                    WHERE type='table' AND name NOT LIKE 'sqlite_%'
                """)

                self.stdout.write('\n📊 Table Information:')
                for (table_name,) in cursor.fetchall():
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    count = cursor.fetchone()[0]
                    self.stdout.write(f'  {table_name}: {count:,} rows')
                
                # Check indexes
                cursor.execute("""
                    SELECT name, tbl_name 
                    FROM sqlite_master 
                    WHERE type='index' AND name NOT LIKE 'sqlite_%'
                    ORDER BY tbl_name, name
                """)
                
                self.stdout.write('\n🗂️  Database Indexes:')
                current_table = None
                for index_name, table_name in cursor.fetchall():
                    if table_name != current_table:
                        self.stdout.write(f'  {table_name}:')
                        current_table = table_name
                    self.stdout.write(f'    - {index_name}')
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'  ❌ Performance analysis error: {e}')
            )

    def show_performance_tips(self):
        """Show performance optimization tips"""
        self.stdout.write('\n💡 Performance Optimization Tips:')
        
        tips = [
            "✅ Database indexes have been optimized for common queries",
            "✅ SQLite settings have been tuned for better performance", 
            "✅ Caching has been enabled for frequently accessed data",
            "✅ Query optimization with select_related() and prefetch_related()",
            "✅ Pagination increased to reduce database round trips",
            "",
            "🔧 Additional recommendations:",
            "  • Monitor query performance using Django Debug Toolbar",
            "  • Consider using database connection pooling for high traffic",
            "  • Regularly run VACUUM and ANALYZE on SQLite database",
            "  • Monitor cache hit rates and adjust cache timeouts as needed",
            "  • Use database query logging to identify slow queries",
        ]
        
        for tip in tips:
            if tip.startswith('✅') or tip.startswith('🔧'):
                self.stdout.write(self.style.SUCCESS(f'  {tip}'))
            elif tip.startswith('  •'):
                self.stdout.write(f'  {tip}')
            else:
                self.stdout.write(tip)
