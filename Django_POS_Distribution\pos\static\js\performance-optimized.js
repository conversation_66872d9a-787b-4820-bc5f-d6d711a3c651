/**
 * Performance-optimized JavaScript utilities for POS system
 */

// Debounce utility to prevent excessive API calls
function debounce(func, wait, immediate) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            timeout = null;
            if (!immediate) func(...args);
        };
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func(...args);
    };
}

// Throttle utility for scroll events
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// Simple client-side cache
class SimpleCache {
    constructor(maxSize = 100, ttl = 300000) { // 5 minutes default TTL
        this.cache = new Map();
        this.maxSize = maxSize;
        this.ttl = ttl;
    }

    set(key, value) {
        // Remove oldest entries if cache is full
        if (this.cache.size >= this.maxSize) {
            const firstKey = this.cache.keys().next().value;
            this.cache.delete(firstKey);
        }

        this.cache.set(key, {
            value: value,
            timestamp: Date.now()
        });
    }

    get(key) {
        const item = this.cache.get(key);
        if (!item) return null;

        // Check if item has expired
        if (Date.now() - item.timestamp > this.ttl) {
            this.cache.delete(key);
            return null;
        }

        return item.value;
    }

    clear() {
        this.cache.clear();
    }

    size() {
        return this.cache.size;
    }
}

// Global cache instance
const apiCache = new SimpleCache(50, 120000); // 2 minutes TTL

// Optimized fetch with caching and error handling
async function cachedFetch(url, options = {}) {
    const cacheKey = `${url}_${JSON.stringify(options)}`;
    
    // Try cache first for GET requests
    if (!options.method || options.method === 'GET') {
        const cached = apiCache.get(cacheKey);
        if (cached) {
            return cached;
        }
    }

    try {
        const response = await fetch(url, {
            ...options,
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                ...options.headers
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        
        // Cache successful GET responses
        if (!options.method || options.method === 'GET') {
            apiCache.set(cacheKey, data);
        }

        return data;
    } catch (error) {
        console.error('Fetch error:', error);
        throw error;
    }
}

// Virtual scrolling for large lists
class VirtualScroller {
    constructor(container, itemHeight, renderItem) {
        this.container = container;
        this.itemHeight = itemHeight;
        this.renderItem = renderItem;
        this.items = [];
        this.visibleStart = 0;
        this.visibleEnd = 0;
        this.scrollTop = 0;
        
        this.init();
    }

    init() {
        this.container.style.position = 'relative';
        this.container.style.overflow = 'auto';
        
        this.viewport = document.createElement('div');
        this.viewport.style.position = 'absolute';
        this.viewport.style.top = '0';
        this.viewport.style.left = '0';
        this.viewport.style.right = '0';
        
        this.container.appendChild(this.viewport);
        
        this.container.addEventListener('scroll', throttle(() => {
            this.handleScroll();
        }, 16)); // ~60fps
    }

    setItems(items) {
        this.items = items;
        this.container.style.height = `${items.length * this.itemHeight}px`;
        this.handleScroll();
    }

    handleScroll() {
        this.scrollTop = this.container.scrollTop;
        const containerHeight = this.container.clientHeight;
        
        this.visibleStart = Math.floor(this.scrollTop / this.itemHeight);
        this.visibleEnd = Math.min(
            this.visibleStart + Math.ceil(containerHeight / this.itemHeight) + 1,
            this.items.length
        );
        
        this.render();
    }

    render() {
        this.viewport.innerHTML = '';
        this.viewport.style.transform = `translateY(${this.visibleStart * this.itemHeight}px)`;
        
        for (let i = this.visibleStart; i < this.visibleEnd; i++) {
            if (this.items[i]) {
                const element = this.renderItem(this.items[i], i);
                element.style.height = `${this.itemHeight}px`;
                this.viewport.appendChild(element);
            }
        }
    }
}

// Optimized product search with debouncing and caching
class ProductSearch {
    constructor(searchInput, resultsContainer, options = {}) {
        this.searchInput = searchInput;
        this.resultsContainer = resultsContainer;
        this.options = {
            debounceDelay: 300,
            minSearchLength: 2,
            maxResults: 20,
            ...options
        };
        
        this.currentRequest = null;
        this.init();
    }

    init() {
        const debouncedSearch = debounce(
            this.performSearch.bind(this),
            this.options.debounceDelay
        );

        this.searchInput.addEventListener('input', (e) => {
            const query = e.target.value.trim();
            if (query.length >= this.options.minSearchLength) {
                debouncedSearch(query);
            } else {
                this.clearResults();
            }
        });
    }

    async performSearch(query) {
        // Cancel previous request
        if (this.currentRequest) {
            this.currentRequest.abort();
        }

        const controller = new AbortController();
        this.currentRequest = controller;

        try {
            this.showLoading();
            
            const params = new URLSearchParams({
                search: query,
                limit: this.options.maxResults
            });

            const data = await cachedFetch(
                `/api/products/search/?${params}`,
                { signal: controller.signal }
            );

            if (!controller.signal.aborted) {
                this.displayResults(data.products || []);
            }
        } catch (error) {
            if (error.name !== 'AbortError') {
                console.error('Search error:', error);
                this.showError('Search failed. Please try again.');
            }
        } finally {
            this.currentRequest = null;
        }
    }

    showLoading() {
        this.resultsContainer.innerHTML = '<div class="loading">Searching...</div>';
    }

    showError(message) {
        this.resultsContainer.innerHTML = `<div class="error">${message}</div>`;
    }

    displayResults(products) {
        if (products.length === 0) {
            this.resultsContainer.innerHTML = '<div class="no-results">No products found</div>';
            return;
        }

        const html = products.map(product => `
            <div class="product-item" data-id="${product.id}">
                <div class="product-name">${product.name}</div>
                <div class="product-price">₱${product.price}</div>
                <div class="product-stock">Stock: ${product.stock_quantity}</div>
            </div>
        `).join('');

        this.resultsContainer.innerHTML = html;
    }

    clearResults() {
        this.resultsContainer.innerHTML = '';
    }
}

// Optimized infinite scroll
class InfiniteScroll {
    constructor(container, loadMore, options = {}) {
        this.container = container;
        this.loadMore = loadMore;
        this.options = {
            threshold: 100,
            debounceDelay: 100,
            ...options
        };
        
        this.loading = false;
        this.hasMore = true;
        this.page = 1;
        
        this.init();
    }

    init() {
        const debouncedScroll = debounce(
            this.handleScroll.bind(this),
            this.options.debounceDelay
        );

        this.container.addEventListener('scroll', debouncedScroll);
    }

    handleScroll() {
        if (this.loading || !this.hasMore) return;

        const { scrollTop, scrollHeight, clientHeight } = this.container;
        
        if (scrollTop + clientHeight >= scrollHeight - this.options.threshold) {
            this.loadNextPage();
        }
    }

    async loadNextPage() {
        if (this.loading) return;

        this.loading = true;
        this.showLoadingIndicator();

        try {
            const data = await this.loadMore(this.page + 1);
            
            if (data && data.products) {
                this.page++;
                this.hasMore = data.has_more;
                this.appendResults(data.products);
            } else {
                this.hasMore = false;
            }
        } catch (error) {
            console.error('Load more error:', error);
        } finally {
            this.loading = false;
            this.hideLoadingIndicator();
        }
    }

    showLoadingIndicator() {
        let indicator = this.container.querySelector('.loading-indicator');
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.className = 'loading-indicator';
            indicator.textContent = 'Loading more...';
            this.container.appendChild(indicator);
        }
        indicator.style.display = 'block';
    }

    hideLoadingIndicator() {
        const indicator = this.container.querySelector('.loading-indicator');
        if (indicator) {
            indicator.style.display = 'none';
        }
    }

    appendResults(items) {
        // This should be implemented by the specific use case
        console.log('Append results:', items);
    }

    reset() {
        this.page = 1;
        this.hasMore = true;
        this.loading = false;
    }
}

// Performance monitoring
class PerformanceMonitor {
    constructor() {
        this.metrics = new Map();
    }

    startTimer(name) {
        this.metrics.set(name, performance.now());
    }

    endTimer(name) {
        const startTime = this.metrics.get(name);
        if (startTime) {
            const duration = performance.now() - startTime;
            console.log(`${name}: ${duration.toFixed(2)}ms`);
            this.metrics.delete(name);
            return duration;
        }
        return null;
    }

    measureFunction(func, name) {
        return (...args) => {
            this.startTimer(name);
            const result = func.apply(this, args);
            this.endTimer(name);
            return result;
        };
    }
}

// Global performance monitor
const perfMonitor = new PerformanceMonitor();

// Export utilities for use in other scripts
window.PosUtils = {
    debounce,
    throttle,
    SimpleCache,
    cachedFetch,
    VirtualScroller,
    ProductSearch,
    InfiniteScroll,
    PerformanceMonitor,
    perfMonitor,
    apiCache
};
