<!-- SmartPOS Navigation Bar -->
<div class="bg-gradient-to-r from-blue-600 to-blue-700 rounded-lg shadow-lg mb-6">
    <div class="px-6 py-4">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-8">
                <!-- SmartPOS Brand -->
                <div class="flex items-center space-x-2">
                    <div class="bg-white p-2 rounded-lg">
                        <i class="fas fa-cash-register text-blue-600 text-xl"></i>
                    </div>
                    <span class="text-white text-xl font-bold">SmartPOS</span>
                </div>

                <!-- Navigation Links -->
                <nav class="hidden md:flex items-center space-x-4">
                    <a href="{% url 'landing' %}" class="flex items-center space-x-2 text-white hover:text-blue-200 transition-colors duration-200 {% if request.resolver_match.url_name == 'landing' %}text-blue-200 bg-blue-800 bg-opacity-50 px-3 py-2 rounded-lg{% endif %}">
                        <i class="fas fa-home"></i>
                        <span class="font-medium">Home</span>
                    </a>
                    <a href="{% url 'dashboard' %}" class="flex items-center space-x-2 text-white hover:text-blue-200 transition-colors duration-200 {% if request.resolver_match.url_name == 'dashboard' %}text-blue-200 bg-blue-800 bg-opacity-50 px-3 py-2 rounded-lg{% endif %}">
                        <i class="fas fa-tachometer-alt"></i>
                        <span class="font-medium">Dashboard</span>
                    </a>
                    <a href="{% url 'create_sale' %}" class="flex items-center space-x-2 text-white hover:text-blue-200 transition-colors duration-200 {% if request.resolver_match.url_name == 'create_sale' %}text-blue-200 bg-blue-800 bg-opacity-50 px-3 py-2 rounded-lg{% endif %}">
                        <i class="fas fa-shopping-cart"></i>
                        <span class="font-medium">Point of Sale</span>
                    </a>
                    <a href="{% url 'products' %}" class="flex items-center space-x-2 text-white hover:text-blue-200 transition-colors duration-200 {% if request.resolver_match.url_name == 'products' %}text-blue-200 bg-blue-800 bg-opacity-50 px-3 py-2 rounded-lg{% endif %}">
                        <i class="fas fa-box"></i>
                        <span class="font-medium">Products</span>
                    </a>
                    <a href="{% url 'sales_report' %}" class="flex items-center space-x-2 text-white hover:text-blue-200 transition-colors duration-200 {% if request.resolver_match.url_name == 'sales_report' %}text-blue-200 bg-blue-800 bg-opacity-50 px-3 py-2 rounded-lg{% endif %}">
                        <i class="fas fa-chart-bar"></i>
                        <span class="font-medium">Reports</span>
                    </a>
                </nav>
            </div>

            <!-- Mobile Menu Button -->
            <button id="mobileMenuBtn" class="md:hidden text-white hover:text-blue-200 transition-colors duration-200">
                <i class="fas fa-bars text-xl"></i>
            </button>
        </div>

        <!-- Mobile Navigation -->
        <div id="mobileNav" class="hidden md:hidden mt-4 pt-4 border-t border-blue-500">
            <nav class="flex flex-col space-y-3">
                <a href="{% url 'landing' %}" class="flex items-center space-x-2 text-white hover:text-blue-200 transition-colors duration-200 {% if request.resolver_match.url_name == 'landing' %}text-blue-200 bg-blue-800 bg-opacity-50 px-3 py-2 rounded-lg{% endif %}">
                    <i class="fas fa-home"></i>
                    <span class="font-medium">Home</span>
                </a>
                <a href="{% url 'dashboard' %}" class="flex items-center space-x-2 text-white hover:text-blue-200 transition-colors duration-200 {% if request.resolver_match.url_name == 'dashboard' %}text-blue-200 bg-blue-800 bg-opacity-50 px-3 py-2 rounded-lg{% endif %}">
                    <i class="fas fa-tachometer-alt"></i>
                    <span class="font-medium">Dashboard</span>
                </a>
                <a href="{% url 'create_sale' %}" class="flex items-center space-x-2 text-white hover:text-blue-200 transition-colors duration-200 {% if request.resolver_match.url_name == 'create_sale' %}text-blue-200 bg-blue-800 bg-opacity-50 px-3 py-2 rounded-lg{% endif %}">
                    <i class="fas fa-shopping-cart"></i>
                    <span class="font-medium">Point of Sale</span>
                </a>
                <a href="{% url 'products' %}" class="flex items-center space-x-2 text-white hover:text-blue-200 transition-colors duration-200 {% if request.resolver_match.url_name == 'products' %}text-blue-200 bg-blue-800 bg-opacity-50 px-3 py-2 rounded-lg{% endif %}">
                    <i class="fas fa-box"></i>
                    <span class="font-medium">Products</span>
                </a>
                <a href="{% url 'sales_report' %}" class="flex items-center space-x-2 text-white hover:text-blue-200 transition-colors duration-200 {% if request.resolver_match.url_name == 'sales_report' %}text-blue-200 bg-blue-800 bg-opacity-50 px-3 py-2 rounded-lg{% endif %}">
                    <i class="fas fa-chart-bar"></i>
                    <span class="font-medium">Reports</span>
                </a>
            </nav>
        </div>
    </div>
</div>

<script>
    // Mobile menu toggle functionality
    document.addEventListener('DOMContentLoaded', function() {
        const mobileMenuBtn = document.getElementById('mobileMenuBtn');
        const mobileNav = document.getElementById('mobileNav');
        
        if (mobileMenuBtn && mobileNav) {
            mobileMenuBtn.addEventListener('click', function() {
                mobileNav.classList.toggle('hidden');
            });
        }
    });
</script>
