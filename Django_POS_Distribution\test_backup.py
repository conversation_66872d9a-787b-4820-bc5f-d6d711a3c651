#!/usr/bin/env python3
"""
Test script to verify backup functionality
"""

import os
import sys
import django
from pathlib import Path

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'desktop_app_settings')
django.setup()

def test_backup_system():
    """Test the backup system functionality"""
    print("Testing Django POS Backup System")
    print("=" * 40)
    
    # Check backup directory
    backup_dir = Path('backups')
    if backup_dir.exists():
        files = list(backup_dir.iterdir())
        print(f"✅ Backup directory exists")
        print(f"📁 Found {len(files)} backup files:")
        
        total_size = 0
        for f in files:
            size = f.stat().st_size
            total_size += size
            size_mb = size / (1024 * 1024)
            print(f"   - {f.name} ({size_mb:.2f} MB)")
        
        print(f"💾 Total backup size: {total_size / (1024 * 1024):.2f} MB")
    else:
        print("❌ Backup directory not found")
        return False
    
    # Test backup command
    print("\n🔧 Testing backup command...")
    try:
        from django.core.management import call_command
        from io import StringIO
        
        # Capture command output
        out = StringIO()
        call_command('backup_database', '--compress', stdout=out)
        output = out.getvalue()
        
        if "Backup completed successfully" in output:
            print("✅ Backup command executed successfully")
        else:
            print("⚠️  Backup command completed with warnings")
            
        print("📋 Command output:")
        for line in output.split('\n')[:10]:  # Show first 10 lines
            if line.strip():
                print(f"   {line}")
                
    except Exception as e:
        print(f"❌ Error testing backup command: {e}")
        return False
    
    # Test backup API views
    print("\n🌐 Testing backup API...")
    try:
        from pos.views import backup_status_api
        from django.test import RequestFactory
        from django.contrib.auth.models import User
        
        # Create test request
        factory = RequestFactory()
        request = factory.get('/api/backup/status/')
        
        # Create test user
        user = User.objects.first()
        if user:
            request.user = user
            
            # Test API
            response = backup_status_api(request)
            if response.status_code == 200:
                print("✅ Backup status API working")
            else:
                print(f"⚠️  Backup status API returned status {response.status_code}")
        else:
            print("⚠️  No user found for API testing")
            
    except Exception as e:
        print(f"❌ Error testing backup API: {e}")
    
    # Check database data
    print("\n📊 Testing database backup data...")
    try:
        from pos.models import Category, Product, Sale, SaleItem
        
        stats = {
            'categories': Category.objects.count(),
            'products': Product.objects.count(),
            'sales': Sale.objects.count(),
            'sale_items': SaleItem.objects.count(),
        }
        
        print("📈 Database statistics:")
        for model, count in stats.items():
            print(f"   - {model.title()}: {count} records")
            
        total_records = sum(stats.values())
        if total_records > 0:
            print(f"✅ Total records to backup: {total_records}")
        else:
            print("⚠️  No data found in database")
            
    except Exception as e:
        print(f"❌ Error checking database: {e}")
    
    print("\n🎉 Backup system test completed!")
    return True

if __name__ == "__main__":
    test_backup_system()
