#!/usr/bin/env python3
"""
Test script to verify restore functionality
"""

import os
import sys
import django
from pathlib import Path

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'pos_system.settings')
django.setup()

def test_restore_system():
    """Test the restore system functionality"""
    print("Testing Django POS Restore System")
    print("=" * 40)
    
    # Check backup directory
    backup_dir = Path('backups')
    if backup_dir.exists():
        files = list(backup_dir.iterdir())
        print(f"✅ Backup directory exists")
        print(f"📁 Found {len(files)} backup files:")
        
        for f in files:
            if f.is_file():
                size = f.stat().st_size
                size_mb = size / (1024 * 1024)
                print(f"   - {f.name} ({size_mb:.2f} MB)")
        
        if files:
            # Test restore command with the first backup file
            test_file = files[0]
            print(f"\n🔧 Testing restore command with: {test_file.name}")
            
            try:
                from django.core.management import call_command
                from io import StringIO
                
                # Test restore command (dry run)
                print("📋 Testing restore command help...")
                out = StringIO()
                call_command('restore_backup', '--help', stdout=out)
                output = out.getvalue()
                
                if "Restore POS system from backup file" in output:
                    print("✅ Restore command is available and working")
                else:
                    print("⚠️  Restore command help output unexpected")
                    
            except Exception as e:
                print(f"❌ Error testing restore command: {e}")
                return False
        else:
            print("⚠️  No backup files found to test restore")
    else:
        print("❌ Backup directory not found")
        return False
    
    # Test restore API views
    print("\n🌐 Testing restore API...")
    try:
        from pos.views import restore_backup_api
        from django.test import RequestFactory
        from django.contrib.auth.models import User
        
        # Create test request
        factory = RequestFactory()
        request = factory.post('/api/backup/restore/test.zip')
        
        # Create test user
        user = User.objects.first()
        if user:
            request.user = user
            print("✅ Restore API function exists and is accessible")
        else:
            print("⚠️  No user found for API testing")
            
    except Exception as e:
        print(f"❌ Error testing restore API: {e}")
    
    # Check database data
    print("\n📊 Current database status...")
    try:
        from pos.models import Category, Product, Sale, SaleItem
        
        stats = {
            'categories': Category.objects.count(),
            'products': Product.objects.count(),
            'sales': Sale.objects.count(),
            'sale_items': SaleItem.objects.count(),
        }
        
        print("📈 Current database statistics:")
        for model, count in stats.items():
            print(f"   - {model.title()}: {count} records")
            
        total_records = sum(stats.values())
        if total_records > 0:
            print(f"✅ Total records in database: {total_records}")
        else:
            print("⚠️  No data found in database")
            
    except Exception as e:
        print(f"❌ Error checking database: {e}")
    
    print("\n🎉 Restore system test completed!")
    print("\nTo test restore functionality:")
    print("1. Go to http://127.0.0.1:8000/backup/")
    print("2. Find a backup file in the list")
    print("3. Click the green 'Restore' button (undo icon)")
    print("4. Confirm the restore operation")
    print("5. Wait for completion and restart if prompted")
    
    return True

if __name__ == "__main__":
    test_restore_system()
