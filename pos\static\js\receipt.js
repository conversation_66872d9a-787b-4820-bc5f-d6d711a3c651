
        // Select all functionality
        document.getElementById('selectAll').addEventListener('change', function() {
            document.querySelectorAll('.sale-checkbox').forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });

        // Individual delete
        function deleteSale(saleId) {
            if (confirm('Are you sure you want to delete this sale?')) {
                fetch(`/api/sales/${saleId}/delete/`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRFToken': getCookie('csrftoken')
                    }
                })
                .then(response => {
                    if (response.ok) {
                        window.location.reload();
                    }
                });
            }
        }

        // Bulk delete
        function bulkDelete() {
            const selectedSales = Array.from(document.querySelectorAll('.sale-checkbox:checked'))
                .map(checkbox => checkbox.value);
        
            if (selectedSales.length === 0) {
                alert('Please select sales to delete');
                return;
            }

            if (confirm(`Are you sure you want to delete ${selectedSales.length} sales?`)) {
                fetch('/api/sales/bulk-delete/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    body: JSON.stringify({sale_ids: selectedSales})
                })
                .then(response => {
                    if (response.ok) {
                        window.location.reload();
                    }
                });
            }
        }

        // CSRF token helper
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
          function formatNumber(number) {
              return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
          }

          function reprintReceipt(saleId) {
              fetch(`/api/receipt/${saleId}/`)
                  .then(response => response.json())
                  .then(data => {
                      // Use the same format as create.html receipt template
                      const receiptHTML = `
                          <!DOCTYPE html>
                          <html>
                          <head>
                              <title>Receipt #${data.id}</title>
                              <style>
                                  body {
                                      font-family: 'Courier New', monospace;
                                      width: 57mm;
                                      margin: 0 auto;
                                      padding: 5mm;
                                      background-color: #fff;
                                  }
                              </style>
                          </head>
                          <body>
                              <div style="font-family: 'Courier New', monospace; width: 57mm; margin: 0 auto; padding: 5mm; background-color: #fff;">
                                  <div style="text-align: left; margin-bottom: 3mm; border-bottom: 1px solid #000; padding-bottom: 2mm;">
                                      <h2 style="font-size: 12pt; margin-bottom: 2mm; text-align: center;">REFERENCE RECEIPT</h2>
                                      <p style="font-size: 8pt; margin: 1mm 0;">Date: ${new Date(data.created_at).toLocaleDateString()}</p>
                                      <p style="font-size: 8pt; margin: 1mm 0;">Time: ${new Date(data.created_at).toLocaleTimeString()}</p>
                                      <p style="font-size: 8pt; margin: 1mm 0;">Receipt #: ${data.id}</p>
                                      <p style="font-size: 8pt; margin: 1mm 0;">Ref #: ${data.payment_reference}</p>
                                  </div>

                                  <div style="margin-bottom: 2mm; border-bottom: 1px dashed #000; padding-bottom: 2mm; font-size: 8pt; text-transform: uppercase;">
                                      ${data.items.map(item => `
                                          <div style="display: flex; justify-content: space-between; margin: 1mm 0;">
                                              <span>${item.product_name} × ${item.quantity}</span>
                                              <span>₱${formatNumber((item.quantity * item.price_at_sale).toFixed(2))}</span>
                                          </div>
                                      `).join('')}
                                  </div>

                                  <div style="border-top: 1px solid #000; padding-top: 2mm; margin-top: 2mm;">
                                      <div style="display: flex; justify-content: space-between; margin: 1mm 0; font-size: 8pt;">
                                          <span>Subtotal:</span>
                                          <span>₱${formatNumber(parseFloat(data.subtotal).toFixed(2))}</span>
                                      </div>
                                      <div style="display: flex; justify-content: space-between; margin: 1mm 0; font-size: 8pt;">
                                          <span>Tax (12%):</span>
                                          <span>₱${formatNumber(parseFloat(data.tax_amount).toFixed(2))}</span>
                                      </div>
                                      <div style="display: flex; justify-content: space-between; font-weight: bold; margin-top: 2mm; padding-top: 2mm; border-top: 1px solid #000; font-size: 10pt;">
                                          <span>Total:</span>
                                          <span>₱${formatNumber(parseFloat(data.total_amount).toFixed(2))}</span>
                                      </div>
                                  </div>

                                  <div style="text-align: center; margin-top: 3mm; padding-top: 2mm; border-top: 1px solid #000;">
                                      <p style="font-size: 8pt;">Thank you for your purchase!</p>

                                      <!-- Reference Only Disclaimer -->
                                      <div style="margin-top: 3mm; padding-top: 2mm; border-top: 2px solid #000; font-size: 8pt; text-align: center; background-color: #f0f0f0;">
                                          <p style="margin: 1mm 0; font-weight: bold; font-size: 9pt;">*** FOR REFERENCE ONLY ***</p>
                                      </div>

                                      <!-- BIR Compliance Template (For Future Use) -->
                                      <div style="margin-top: 2mm; padding-top: 2mm; border-top: 1px dashed #000; font-size: 6pt; text-align: center; color: #888;">
                                          <p style="margin: 0.5mm 0;">When registered with BIR, update:</p>
                                          <p style="margin: 0.5mm 0;">TIN: [Your TIN Number]</p>
                                          <p style="margin: 0.5mm 0;">Permit No: [ATP Number]</p>
                                          <p style="margin: 0.5mm 0;">Date Issued: [ATP Date]</p>
                                      </div>
                                  </div>
                              </div>
                          </body>
                          </html>
                      `;

                      const printWindow = window.open('', 'Print Receipt', 'height=600,width=800');
                      printWindow.document.write(receiptHTML);
                      printWindow.document.close();
                      printWindow.print();
                  });
          }
    // Add this at the top of the file
    document.addEventListener('DOMContentLoaded', function() {
        const searchForm = document.getElementById('searchForm');
        const searchInput = document.getElementById('searchInput');
    
        searchForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const searchTerm = searchInput.value.trim();
            
            // Update URL with search parameter
            const currentUrl = new URL(window.location.href);
            currentUrl.searchParams.set('search', searchTerm);
            
            // Redirect to filtered results
            window.location.href = currentUrl.toString();
        });
    
        // Preserve search term in input after page reload
        const urlParams = new URLSearchParams(window.location.search);
        const searchTerm = urlParams.get('search');
        if (searchTerm) {
            searchInput.value = searchTerm;
        }
    });
    