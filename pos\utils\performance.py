"""
Performance monitoring utilities for POS system
"""

import time
import logging
from functools import wraps
from django.core.cache import cache
from django.db import connection
from django.conf import settings

logger = logging.getLogger(__name__)


def monitor_query_performance(func):
    """Decorator to monitor database query performance"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        if not settings.DEBUG:
            return func(*args, **kwargs)
            
        # Reset query count
        initial_queries = len(connection.queries)
        start_time = time.time()
        
        # Execute function
        result = func(*args, **kwargs)
        
        # Calculate metrics
        end_time = time.time()
        execution_time = end_time - start_time
        query_count = len(connection.queries) - initial_queries
        
        # Log performance metrics
        logger.info(
            f"Performance: {func.__name__} - "
            f"Time: {execution_time:.3f}s, "
            f"Queries: {query_count}"
        )
        
        # Log slow queries
        if execution_time > 1.0:  # Log if takes more than 1 second
            logger.warning(
                f"Slow function: {func.__name__} took {execution_time:.3f}s "
                f"with {query_count} queries"
            )
        
        return result
    return wrapper


def cache_result(cache_key_prefix, timeout=300):
    """Decorator to cache function results"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Create cache key from function name and arguments
            cache_key = f"{cache_key_prefix}_{hash(str(args) + str(kwargs))}"
            
            # Try to get cached result
            result = cache.get(cache_key)
            if result is not None:
                return result
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            cache.set(cache_key, result, timeout)
            
            return result
        return wrapper
    return decorator


class PerformanceMonitor:
    """Context manager for monitoring performance"""
    
    def __init__(self, operation_name):
        self.operation_name = operation_name
        self.start_time = None
        self.initial_queries = None
    
    def __enter__(self):
        self.start_time = time.time()
        self.initial_queries = len(connection.queries)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        end_time = time.time()
        execution_time = end_time - self.start_time
        query_count = len(connection.queries) - self.initial_queries
        
        logger.info(
            f"Operation: {self.operation_name} - "
            f"Time: {execution_time:.3f}s, "
            f"Queries: {query_count}"
        )


def get_cache_stats():
    """Get cache performance statistics"""
    try:
        # This is a simple implementation - in production you might want
        # to use more sophisticated cache monitoring
        cache_info = {
            'backend': cache.__class__.__name__,
            'location': getattr(cache, '_cache', {}).get('_location', 'Unknown'),
        }
        return cache_info
    except Exception as e:
        logger.error(f"Error getting cache stats: {e}")
        return {}


def get_database_stats():
    """Get database performance statistics"""
    try:
        with connection.cursor() as cursor:
            # Get database size (SQLite specific)
            cursor.execute("PRAGMA page_count;")
            page_count = cursor.fetchone()[0]
            
            cursor.execute("PRAGMA page_size;")
            page_size = cursor.fetchone()[0]
            
            db_size = page_count * page_size
            
            # Get table statistics
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name NOT LIKE 'sqlite_%'
            """)
            
            table_stats = {}
            for (table_name,) in cursor.fetchall():
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                table_stats[table_name] = count
            
            return {
                'database_size': db_size,
                'database_size_mb': round(db_size / (1024 * 1024), 2),
                'table_counts': table_stats,
                'total_records': sum(table_stats.values())
            }
            
    except Exception as e:
        logger.error(f"Error getting database stats: {e}")
        return {}


def optimize_queryset(queryset, select_related_fields=None, prefetch_related_fields=None):
    """Optimize a queryset with select_related and prefetch_related"""
    
    if select_related_fields:
        queryset = queryset.select_related(*select_related_fields)
    
    if prefetch_related_fields:
        queryset = queryset.prefetch_related(*prefetch_related_fields)
    
    return queryset


def batch_process(queryset, batch_size=1000, callback=None):
    """Process large querysets in batches to avoid memory issues"""
    
    total_count = queryset.count()
    processed = 0
    
    logger.info(f"Starting batch processing of {total_count} records")
    
    for start in range(0, total_count, batch_size):
        end = min(start + batch_size, total_count)
        batch = queryset[start:end]
        
        if callback:
            callback(batch, start, end, total_count)
        
        processed += len(batch)
        
        if processed % (batch_size * 10) == 0:  # Log every 10 batches
            logger.info(f"Processed {processed}/{total_count} records")
    
    logger.info(f"Batch processing completed: {processed} records")


class QueryCounter:
    """Context manager to count database queries"""
    
    def __init__(self):
        self.initial_count = 0
        self.final_count = 0
    
    def __enter__(self):
        self.initial_count = len(connection.queries)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.final_count = len(connection.queries)
    
    @property
    def query_count(self):
        return self.final_count - self.initial_count


def log_slow_queries(threshold=0.1):
    """Log queries that take longer than threshold seconds"""
    if not settings.DEBUG:
        return
    
    for query in connection.queries:
        time_taken = float(query['time'])
        if time_taken > threshold:
            logger.warning(
                f"Slow query ({time_taken:.3f}s): {query['sql'][:200]}..."
            )


def clear_performance_cache():
    """Clear performance-related cache entries"""
    cache_keys = [
        'dashboard_data_*',
        'all_categories',
        'search_*',
        'load_more_products_*'
    ]
    
    for pattern in cache_keys:
        # Note: This is a simplified implementation
        # In production, you might want to use cache.delete_many() or similar
        try:
            cache.delete(pattern)
        except:
            pass
    
    logger.info("Performance cache cleared")


# Performance monitoring middleware
class PerformanceMiddleware:
    """Middleware to monitor request performance"""
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        start_time = time.time()
        initial_queries = len(connection.queries)
        
        response = self.get_response(request)
        
        end_time = time.time()
        execution_time = end_time - start_time
        query_count = len(connection.queries) - initial_queries
        
        # Add performance headers
        response['X-Response-Time'] = f"{execution_time:.3f}s"
        response['X-Query-Count'] = str(query_count)
        
        # Log slow requests
        if execution_time > 2.0:  # Log requests taking more than 2 seconds
            logger.warning(
                f"Slow request: {request.path} took {execution_time:.3f}s "
                f"with {query_count} queries"
            )
        
        return response
