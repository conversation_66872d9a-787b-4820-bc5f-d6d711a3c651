// Product Modal Functions
function openModal() {
    document.getElementById('addProductModal').classList.remove('hidden');
}

function closeModal() {
    document.getElementById('addProductModal').classList.add('hidden');
}

function openEditModal(id, name, barcode, categoryId, price, stock, description, expirationDate) {
    const form = document.getElementById('editProductForm');
    form.action = `/products/edit/${id}/`;
    
    // Properly escape the values to handle special characters
    document.getElementById('edit_name').value = name.replace(/"/g, '"');
    document.getElementById('edit_barcode').value = barcode.replace(/"/g, '"');
    document.getElementById('edit_category').value = categoryId;
    document.getElementById('edit_price').value = price;
    document.getElementById('edit_stock_quantity').value = stock;
    document.getElementById('edit_description').value = description.replace(/"/g, '"');
    document.getElementById('edit_expiration_date').value = expirationDate || '';
    
    document.getElementById('editProductModal').classList.remove('hidden');
}
function closeEditModal() {
    document.getElementById('editProductModal').classList.add('hidden');
}

// Delete All Products Function
function confirmDeleteAllProducts() {
    // Get product count from the page
    const productCards = document.querySelectorAll('.product-card');
    const productCount = productCards.length;

    if (productCount === 0) {
        alert('No products to delete.');
        return;
    }

    const confirmMessage = `⚠️ CRITICAL WARNING: This will permanently delete ALL ${productCount} products!\n\n` +
                         `This action will:\n` +
                         `• Remove all product data\n` +
                         `• Delete all associated inventory records\n` +
                         `• Cannot be undone\n\n` +
                         `Are you absolutely sure you want to continue?`;

    if (confirm(confirmMessage)) {
        // Double confirmation for safety
        const doubleConfirm = confirm(`🚨 FINAL CONFIRMATION\n\nYou are about to delete ${productCount} products permanently.\n\nThis is your last chance to cancel.\n\nProceed with deletion?`);

        if (doubleConfirm) {
            // Show loading state
            const deleteBtn = event.target;
            const originalText = deleteBtn.innerHTML;
            deleteBtn.innerHTML = '<svg class="animate-spin h-6 w-6 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Deleting...';
            deleteBtn.disabled = true;

            // Create and submit form
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/products/delete-all/';

            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = 'csrfmiddlewaretoken';
            csrfToken.value = document.querySelector('[name=csrfmiddlewaretoken]').value;
            form.appendChild(csrfToken);

            document.body.appendChild(form);
            form.submit();
        }
    }
}

// Event Listeners
document.addEventListener('DOMContentLoaded', function() {
    // Close modal when clicking outside
    window.onclick = function(event) {
        const addModal = document.getElementById('addProductModal');
        const editModal = document.getElementById('editProductModal');
        if (event.target == addModal) {
            closeModal();
        }
        if (event.target == editModal) {
            closeEditModal();
        }
    }
});
