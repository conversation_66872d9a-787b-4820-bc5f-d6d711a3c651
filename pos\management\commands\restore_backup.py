"""
Django management command to restore from backup
Usage: python manage.py restore_backup backup_file.zip
"""

import os
import json
import shutil
import zipfile
import tempfile
from pathlib import Path
from django.core.management.base import BaseCommand
from django.core import serializers
from django.conf import settings
from django.db import transaction
from pos.models import Category, Product, Sale, SaleItem

class Command(BaseCommand):
    help = 'Restore POS system from backup file'

    def add_arguments(self, parser):
        parser.add_argument(
            'backup_file',
            type=str,
            help='Path to backup file (.zip) or backup directory',
        )
        parser.add_argument(
            '--confirm',
            action='store_true',
            help='Skip confirmation prompt',
        )
        parser.add_argument(
            '--data-only',
            action='store_true',
            help='Restore only database data (not files)',
        )
        parser.add_argument(
            '--files-only',
            action='store_true',
            help='Restore only files (not database data)',
        )

    def handle(self, *args, **options):
        self.backup_file = options['backup_file']
        self.confirm = options['confirm']
        self.data_only = options['data_only']
        self.files_only = options['files_only']
        
        self.stdout.write(self.style.SUCCESS('POS System Backup Restore'))
        self.stdout.write('=' * 40)
        
        # Validate backup file
        if not self.validate_backup_file():
            return
        
        # Show warning and get confirmation
        if not self.get_confirmation():
            self.stdout.write('Restore cancelled.')
            return
        
        # Perform restore
        self.restore_backup()

    def validate_backup_file(self):
        """Validate backup file exists and is valid"""
        backup_path = Path(self.backup_file)
        
        if not backup_path.exists():
            self.stdout.write(
                self.style.ERROR(f'Backup file not found: {self.backup_file}')
            )
            return False
        
        if backup_path.is_file() and not backup_path.suffix == '.zip':
            self.stdout.write(
                self.style.ERROR('Backup file must be a .zip file or directory')
            )
            return False
        
        self.stdout.write(f'✅ Backup file found: {backup_path.name}')
        return True

    def get_confirmation(self):
        """Get user confirmation for restore"""
        if self.confirm:
            return True
        
        self.stdout.write()
        self.stdout.write(self.style.WARNING('⚠️  WARNING: This will replace all current data!'))
        self.stdout.write()
        self.stdout.write('This restore operation will:')
        
        if not self.files_only:
            self.stdout.write('  - Replace all products, categories, and sales data')
            self.stdout.write('  - Overwrite the current database')
        
        if not self.data_only:
            self.stdout.write('  - Replace media files (product images)')
            self.stdout.write('  - Overwrite static files')
        
        self.stdout.write()
        self.stdout.write('Current data will be PERMANENTLY LOST!')
        self.stdout.write()
        
        response = input('Are you sure you want to continue? (type "yes" to confirm): ')
        return response.lower() == 'yes'

    def restore_backup(self):
        """Restore from backup file"""
        backup_path = Path(self.backup_file)
        
        try:
            if backup_path.is_file():
                # Extract ZIP file
                with tempfile.TemporaryDirectory() as temp_dir:
                    self.stdout.write('📦 Extracting backup file...')
                    with zipfile.ZipFile(backup_path, 'r') as zip_ref:
                        zip_ref.extractall(temp_dir)
                    
                    # Find backup directory in extracted files
                    temp_path = Path(temp_dir)
                    backup_dirs = [d for d in temp_path.iterdir() if d.is_dir() and d.name.startswith('pos_backup_')]
                    
                    if backup_dirs:
                        self.restore_from_directory(backup_dirs[0])
                    else:
                        # Files might be extracted directly
                        self.restore_from_directory(temp_path)
            else:
                # Restore from directory
                self.restore_from_directory(backup_path)
                
            self.stdout.write()
            self.stdout.write(self.style.SUCCESS('🎉 Restore completed successfully!'))
            self.stdout.write('Please restart the POS system to ensure all changes take effect.')
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Restore failed: {e}')
            )

    def restore_from_directory(self, backup_dir):
        """Restore from backup directory"""
        self.stdout.write(f'📁 Restoring from: {backup_dir}')
        
        if not self.files_only:
            # Restore database data
            self.restore_database_data(backup_dir)
            
            # Restore database file
            self.restore_database_file(backup_dir)
        
        if not self.data_only:
            # Restore media files
            self.restore_media_files(backup_dir)
            
            # Restore static files
            self.restore_static_files(backup_dir)

    def restore_database_data(self, backup_dir):
        """Restore database data from JSON files"""
        self.stdout.write('💾 Restoring database data...')
        
        data_dir = backup_dir / 'data'
        if not data_dir.exists():
            self.stdout.write('  ⚠️  No data directory found in backup')
            return
        
        # Model restoration order (to handle dependencies)
        models_to_restore = [
            ('categories', Category),
            ('products', Product),
            ('sales', Sale),
            ('sale_items', SaleItem),
        ]
        
        try:
            with transaction.atomic():
                for name, model in models_to_restore:
                    json_file = data_dir / f'{name}.json'
                    
                    if json_file.exists():
                        self.stdout.write(f'  📋 Restoring {name}...')
                        
                        # Clear existing data
                        model.objects.all().delete()
                        
                        # Load and restore data
                        with open(json_file, 'r', encoding='utf-8') as f:
                            data = f.read()
                        
                        objects = serializers.deserialize('json', data)
                        restored_count = 0
                        
                        for obj in objects:
                            obj.save()
                            restored_count += 1
                        
                        self.stdout.write(f'    ✅ Restored {restored_count} {name}')
                    else:
                        self.stdout.write(f'    ⚠️  No {name}.json found')
                        
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'  ❌ Error restoring database data: {e}')
            )

    def restore_database_file(self, backup_dir):
        """Restore SQLite database file"""
        self.stdout.write('🗄️  Restoring database file...')
        
        try:
            db_backup_dir = backup_dir / 'database'
            backup_db_file = db_backup_dir / 'db.sqlite3'
            
            if backup_db_file.exists():
                current_db_path = Path(settings.DATABASES['default']['NAME'])
                
                # Backup current database
                if current_db_path.exists():
                    backup_current = current_db_path.with_suffix('.sqlite3.backup')
                    shutil.copy2(current_db_path, backup_current)
                    self.stdout.write(f'  📋 Current database backed up to: {backup_current.name}')
                
                # Restore database file
                shutil.copy2(backup_db_file, current_db_path)
                self.stdout.write('  ✅ Database file restored')
            else:
                self.stdout.write('  ⚠️  No database file found in backup')
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'  ❌ Error restoring database file: {e}')
            )

    def restore_media_files(self, backup_dir):
        """Restore media files"""
        self.stdout.write('📸 Restoring media files...')
        
        try:
            media_backup_dir = backup_dir / 'media'
            
            if media_backup_dir.exists():
                media_root = Path(settings.MEDIA_ROOT) if hasattr(settings, 'MEDIA_ROOT') and settings.MEDIA_ROOT else None
                
                if media_root:
                    # Backup current media
                    if media_root.exists():
                        backup_media = media_root.with_name(f'{media_root.name}_backup')
                        if backup_media.exists():
                            shutil.rmtree(backup_media)
                        shutil.move(media_root, backup_media)
                        self.stdout.write(f'  📋 Current media backed up to: {backup_media.name}')
                    
                    # Restore media files
                    shutil.copytree(media_backup_dir, media_root)
                    
                    file_count = sum(1 for _ in media_root.rglob('*') if _.is_file())
                    self.stdout.write(f'  ✅ Restored {file_count} media files')
                else:
                    self.stdout.write('  ⚠️  Media root not configured')
            else:
                self.stdout.write('  ⚠️  No media files found in backup')
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'  ❌ Error restoring media files: {e}')
            )

    def restore_static_files(self, backup_dir):
        """Restore static files"""
        self.stdout.write('🎨 Restoring static files...')
        
        try:
            static_backup_dir = backup_dir / 'static'
            
            if static_backup_dir.exists():
                static_root = Path(settings.STATIC_ROOT) if hasattr(settings, 'STATIC_ROOT') and settings.STATIC_ROOT else None
                
                if static_root:
                    # Backup current static files
                    if static_root.exists():
                        backup_static = static_root.with_name(f'{static_root.name}_backup')
                        if backup_static.exists():
                            shutil.rmtree(backup_static)
                        shutil.move(static_root, backup_static)
                        self.stdout.write(f'  📋 Current static files backed up to: {backup_static.name}')
                    
                    # Restore static files
                    shutil.copytree(static_backup_dir, static_root)
                    
                    file_count = sum(1 for _ in static_root.rglob('*') if _.is_file())
                    self.stdout.write(f'  ✅ Restored {file_count} static files')
                else:
                    self.stdout.write('  ⚠️  Static root not configured')
            else:
                self.stdout.write('  ⚠️  No static files found in backup')
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'  ❌ Error restoring static files: {e}')
            )
