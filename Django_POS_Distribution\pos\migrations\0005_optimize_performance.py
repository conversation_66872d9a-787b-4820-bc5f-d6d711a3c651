# Generated by Django 5.1.2 on 2025-06-01 11:35

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('pos', '0004_alter_sale_cashier'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='product',
            options={'ordering': ['name']},
        ),
        migrations.AlterModelOptions(
            name='sale',
            options={'ordering': ['-created_at']},
        ),
        migrations.AlterField(
            model_name='category',
            name='name',
            field=models.CharField(db_index=True, max_length=100),
        ),
        migrations.AlterField(
            model_name='product',
            name='barcode',
            field=models.CharField(db_index=True, max_length=100, unique=True),
        ),
        migrations.AlterField(
            model_name='product',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, db_index=True),
        ),
        migrations.AlterField(
            model_name='product',
            name='expiration_date',
            field=models.DateField(blank=True, db_index=True, null=True),
        ),
        migrations.AlterField(
            model_name='product',
            name='name',
            field=models.CharField(db_index=True, max_length=200),
        ),
        migrations.AlterField(
            model_name='product',
            name='price',
            field=models.DecimalField(db_index=True, decimal_places=2, max_digits=10),
        ),
        migrations.AlterField(
            model_name='product',
            name='stock_quantity',
            field=models.IntegerField(db_index=True, default=0),
        ),
        migrations.AlterField(
            model_name='sale',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, db_index=True),
        ),
        migrations.AlterField(
            model_name='sale',
            name='payment_reference',
            field=models.CharField(db_index=True, max_length=100),
        ),
        migrations.AlterField(
            model_name='sale',
            name='payment_status',
            field=models.BooleanField(db_index=True, default=False),
        ),
        migrations.AlterField(
            model_name='sale',
            name='subtotal',
            field=models.DecimalField(db_index=True, decimal_places=2, max_digits=10),
        ),
        migrations.AlterField(
            model_name='sale',
            name='total_amount',
            field=models.DecimalField(db_index=True, decimal_places=2, max_digits=10),
        ),
        migrations.AlterField(
            model_name='saleitem',
            name='quantity',
            field=models.IntegerField(db_index=True),
        ),
        migrations.AddIndex(
            model_name='category',
            index=models.Index(fields=['name'], name='pos_categor_name_318abe_idx'),
        ),
        migrations.AddIndex(
            model_name='category',
            index=models.Index(fields=['created_at'], name='pos_categor_created_d7870c_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['name'], name='pos_product_name_93ae43_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['barcode'], name='pos_product_barcode_d45943_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['category', 'stock_quantity'], name='pos_product_categor_a8415c_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['stock_quantity'], name='pos_product_stock_q_73fd3f_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['price'], name='pos_product_price_4af0a4_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['created_at'], name='pos_product_created_ea1b33_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['expiration_date'], name='pos_product_expirat_622759_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['name', 'barcode'], name='pos_product_name_d83235_idx'),
        ),
        migrations.AddIndex(
            model_name='sale',
            index=models.Index(fields=['created_at'], name='pos_sale_created_73359a_idx'),
        ),
        migrations.AddIndex(
            model_name='sale',
            index=models.Index(fields=['payment_status'], name='pos_sale_payment_e5b274_idx'),
        ),
        migrations.AddIndex(
            model_name='sale',
            index=models.Index(fields=['total_amount'], name='pos_sale_total_a_cc9c32_idx'),
        ),
        migrations.AddIndex(
            model_name='sale',
            index=models.Index(fields=['cashier'], name='pos_sale_cashier_a8c41e_idx'),
        ),
        migrations.AddIndex(
            model_name='sale',
            index=models.Index(fields=['created_at', 'payment_status'], name='pos_sale_created_61272b_idx'),
        ),
        migrations.AddIndex(
            model_name='sale',
            index=models.Index(fields=['payment_reference'], name='pos_sale_payment_552497_idx'),
        ),
        migrations.AddIndex(
            model_name='saleitem',
            index=models.Index(fields=['sale'], name='pos_saleite_sale_id_941f75_idx'),
        ),
        migrations.AddIndex(
            model_name='saleitem',
            index=models.Index(fields=['product'], name='pos_saleite_product_2b3f7d_idx'),
        ),
        migrations.AddIndex(
            model_name='saleitem',
            index=models.Index(fields=['sale', 'product'], name='pos_saleite_sale_id_79aec7_idx'),
        ),
    ]
