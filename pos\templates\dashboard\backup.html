{% extends 'base/base.html' %}
{% load static %}

{% block title %}Data Backup - POS System{% endblock %}

{% block extra_css %}
<style>
    .backup-card {
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border: 2px solid #e2e8f0;
        border-radius: 1rem;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
    }
    
    .backup-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        border-color: #3b82f6;
    }
    
    .backup-status {
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        font-weight: 600;
        font-size: 0.875rem;
    }
    
    .status-success {
        background: linear-gradient(135deg, #10b981 0%, #047857 100%);
        color: white;
    }
    
    .status-warning {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        color: white;
    }
    
    .status-error {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        color: white;
    }
    
    .backup-button {
        background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
        border: 2px solid #3b82f6;
        color: white;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        border-radius: 0.75rem;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
    }
    
    .backup-button:hover {
        background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
    }
    
    .backup-button:disabled {
        background: #9ca3af;
        border-color: #9ca3af;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }
    
    .progress-bar {
        background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
        height: 0.5rem;
        border-radius: 0.25rem;
        transition: width 0.3s ease;
    }
</style>
{% endblock %}

{% block content %}
<!-- SmartPOS Navigation Bar -->
<div class="bg-gradient-to-r from-blue-600 to-blue-700 rounded-lg shadow-lg mb-6">
    <div class="px-6 py-4">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-8">
                <!-- SmartPOS Brand -->
                <div class="flex items-center space-x-2">
                    <div class="bg-white p-2 rounded-lg">
                        <i class="fas fa-cash-register text-blue-600 text-xl"></i>
                    </div>
                    <span class="text-white text-xl font-bold">SmartPOS</span>
                </div>

                <!-- Navigation Links -->
                <nav class="hidden md:flex items-center space-x-6">
                    <a href="{% url 'landing' %}" class="flex items-center space-x-2 text-white hover:text-blue-200 transition-colors duration-200">
                        <i class="fas fa-home"></i>
                        <span class="font-medium">Home</span>
                    </a>
                    <a href="{% url 'dashboard' %}" class="flex items-center space-x-2 text-white hover:text-blue-200 transition-colors duration-200">
                        <i class="fas fa-tachometer-alt"></i>
                        <span class="font-medium">Dashboard</span>
                    </a>
                    <a href="{% url 'create_sale' %}" class="flex items-center space-x-2 text-white hover:text-blue-200 transition-colors duration-200">
                        <i class="fas fa-shopping-cart"></i>
                        <span class="font-medium">Point of Sale</span>
                    </a>
                </nav>
            </div>

            <!-- Mobile Menu Button -->
            <div class="md:hidden">
                <button type="button" onclick="toggleMobileNav()" class="text-white hover:text-blue-200 focus:outline-none">
                    <i class="fas fa-bars text-xl"></i>
                </button>
            </div>
        </div>

        <!-- Mobile Navigation -->
        <div id="mobileNav" class="hidden md:hidden mt-4 pt-4 border-t border-blue-500">
            <nav class="flex flex-col space-y-3">
                <a href="{% url 'landing' %}" class="flex items-center space-x-2 text-white hover:text-blue-200 transition-colors duration-200">
                    <i class="fas fa-home"></i>
                    <span class="font-medium">Home</span>
                </a>
                <a href="{% url 'dashboard' %}" class="flex items-center space-x-2 text-white hover:text-blue-200 transition-colors duration-200">
                    <i class="fas fa-tachometer-alt"></i>
                    <span class="font-medium">Dashboard</span>
                </a>
                <a href="{% url 'create_sale' %}" class="flex items-center space-x-2 text-white hover:text-blue-200 transition-colors duration-200">
                    <i class="fas fa-shopping-cart"></i>
                    <span class="font-medium">Point of Sale</span>
                </a>
            </nav>
        </div>
    </div>
</div>

<div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 flex items-center">
                    <i class="fas fa-shield-alt text-blue-600 mr-3"></i>
                    Data Backup & Recovery
                </h1>
                <p class="text-gray-600 mt-2">Protect your POS data with automated backups</p>
            </div>
            <div class="flex space-x-3">
                <button onclick="createBackup()" id="backupBtn" class="backup-button flex items-center">
                    <i class="fas fa-download mr-2"></i>
                    Create Backup Now
                </button>
                <button onclick="refreshBackupList()" class="backup-button bg-gray-600 border-gray-600 hover:bg-gray-700">
                    <i class="fas fa-sync-alt mr-2"></i>
                    Refresh
                </button>
            </div>
        </div>
    </div>

    <!-- Backup Status Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <!-- Last Backup -->
        <div class="backup-card p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Last Backup</h3>
                <i class="fas fa-clock text-blue-600 text-xl"></i>
            </div>
            <div id="lastBackupInfo">
                <p class="text-2xl font-bold text-gray-900" id="lastBackupDate">Loading...</p>
                <p class="text-sm text-gray-600" id="lastBackupStatus">Checking status...</p>
            </div>
        </div>

        <!-- Backup Size -->
        <div class="backup-card p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Total Backup Size</h3>
                <i class="fas fa-hdd text-green-600 text-xl"></i>
            </div>
            <div>
                <p class="text-2xl font-bold text-gray-900" id="totalBackupSize">Loading...</p>
                <p class="text-sm text-gray-600" id="backupCount">Calculating...</p>
            </div>
        </div>

        <!-- Auto Backup Status -->
        <div class="backup-card p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Auto Backup</h3>
                <i class="fas fa-robot text-purple-600 text-xl"></i>
            </div>
            <div>
                <span class="backup-status status-success" id="autoBackupStatus">
                    <i class="fas fa-check mr-1"></i>
                    Enabled
                </span>
                <p class="text-sm text-gray-600 mt-2">Daily at 2:00 AM</p>
            </div>
        </div>
    </div>

    <!-- Backup Progress -->
    <div id="backupProgress" class="hidden mb-8">
        <div class="backup-card p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Backup in Progress</h3>
                <div class="flex items-center">
                    <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-2"></div>
                    <span class="text-sm text-gray-600" id="progressText">Initializing...</span>
                </div>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="progress-bar" id="progressBar" style="width: 0%"></div>
            </div>
        </div>
    </div>

    <!-- Backup List -->
    <div class="backup-card p-6">
        <div class="flex items-center justify-between mb-6">
            <h2 class="text-xl font-semibold text-gray-900">Available Backups</h2>
            <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-600">Auto-refresh:</span>
                <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" id="autoRefresh" class="sr-only peer" checked>
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
            </div>
        </div>

        <div id="backupList">
            <div class="text-center py-8">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p class="text-gray-600">Loading backup list...</p>
            </div>
        </div>
    </div>

    <!-- Backup Instructions -->
    <div class="mt-8 backup-card p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <i class="fas fa-info-circle text-blue-600 mr-2"></i>
            Backup Information
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h4 class="font-semibold text-gray-800 mb-2">What's Included:</h4>
                <ul class="text-sm text-gray-600 space-y-1">
                    <li><i class="fas fa-check text-green-600 mr-2"></i>All product data</li>
                    <li><i class="fas fa-check text-green-600 mr-2"></i>Categories and inventory</li>
                    <li><i class="fas fa-check text-green-600 mr-2"></i>Sales records and receipts</li>
                    <li><i class="fas fa-check text-green-600 mr-2"></i>Database files</li>
                    <li><i class="fas fa-check text-green-600 mr-2"></i>Media and static files</li>
                </ul>
            </div>
            <div>
                <h4 class="font-semibold text-gray-800 mb-2">Backup Schedule:</h4>
                <ul class="text-sm text-gray-600 space-y-1">
                    <li><i class="fas fa-clock text-blue-600 mr-2"></i>Daily automatic backup at 2:00 AM</li>
                    <li><i class="fas fa-calendar text-blue-600 mr-2"></i>Keeps 30 days of backups</li>
                    <li><i class="fas fa-compress text-blue-600 mr-2"></i>Compressed for space efficiency</li>
                    <li><i class="fas fa-download text-blue-600 mr-2"></i>Manual backup available anytime</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
let autoRefreshInterval;

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadBackupStatus();
    loadBackupList();
    setupAutoRefresh();
});

function loadBackupStatus() {
    fetch('/api/backup/status/')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('lastBackupDate').textContent =
                    data.last_backup ? data.last_backup.date : 'No backups yet';
                document.getElementById('lastBackupStatus').textContent =
                    data.last_backup ? 'Backup completed successfully' : 'Create your first backup';
                document.getElementById('totalBackupSize').textContent = data.total_size;
                document.getElementById('backupCount').textContent = `${data.backup_count} backup files`;
            }
        })
        .catch(error => {
            console.error('Error loading backup status:', error);
            document.getElementById('lastBackupDate').textContent = 'Error loading';
            document.getElementById('lastBackupStatus').textContent = 'Check console for details';
        });
}

function loadBackupList() {
    const backupList = document.getElementById('backupList');

    // Show loading
    backupList.innerHTML = `
        <div class="text-center py-8">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p class="text-gray-600">Loading backup list...</p>
        </div>
    `;

    // Load real backup data from API
    fetch('/api/backup/status/')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.backups) {
                if (data.backups.length === 0) {
                    backupList.innerHTML = `
                        <div class="text-center py-8">
                            <i class="fas fa-folder-open text-gray-400 text-4xl mb-4"></i>
                            <p class="text-gray-600">No backups found</p>
                            <p class="text-sm text-gray-500">Create your first backup to get started</p>
                        </div>
                    `;
                } else {
                    backupList.innerHTML = `
                        <div class="space-y-4">
                            ${data.backups.map(backup => generateBackupItem(backup.name, backup.size, 'success', backup.date)).join('')}
                        </div>
                    `;
                }
            } else {
                backupList.innerHTML = `
                    <div class="text-center py-8">
                        <i class="fas fa-exclamation-triangle text-red-400 text-4xl mb-4"></i>
                        <p class="text-red-600">Error loading backup list</p>
                        <p class="text-sm text-gray-500">${data.error || 'Unknown error'}</p>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error loading backup list:', error);
            backupList.innerHTML = `
                <div class="text-center py-8">
                    <i class="fas fa-exclamation-triangle text-red-400 text-4xl mb-4"></i>
                    <p class="text-red-600">Error loading backup list</p>
                    <p class="text-sm text-gray-500">${error.message}</p>
                </div>
            `;
        });
}

function generateBackupItem(filename, size, status, date) {
    const statusClass = status === 'success' ? 'status-success' : 
                       status === 'warning' ? 'status-warning' : 'status-error';
    const statusIcon = status === 'success' ? 'fa-check' : 
                      status === 'warning' ? 'fa-exclamation-triangle' : 'fa-times';
    
    return `
        <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-blue-300 transition-colors">
            <div class="flex items-center space-x-4">
                <div class="flex-shrink-0">
                    <i class="fas fa-file-archive text-blue-600 text-xl"></i>
                </div>
                <div>
                    <h4 class="font-medium text-gray-900">${filename}</h4>
                    <p class="text-sm text-gray-600">${date} • ${size}</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <span class="backup-status ${statusClass}">
                    <i class="fas ${statusIcon} mr-1"></i>
                    ${status.charAt(0).toUpperCase() + status.slice(1)}
                </span>
                <div class="flex space-x-2">
                    <button onclick="downloadBackup('${filename}')" class="text-blue-600 hover:text-blue-800 p-2 rounded-lg hover:bg-blue-50">
                        <i class="fas fa-download"></i>
                    </button>
                    <button onclick="restoreBackup('${filename}')" class="text-green-600 hover:text-green-800 p-2 rounded-lg hover:bg-green-50">
                        <i class="fas fa-undo"></i>
                    </button>
                    <button onclick="deleteBackup('${filename}')" class="text-red-600 hover:text-red-800 p-2 rounded-lg hover:bg-red-50">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    `;
}

function createBackup() {
    const backupBtn = document.getElementById('backupBtn');
    const progressDiv = document.getElementById('backupProgress');
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');

    // Disable button and show progress
    backupBtn.disabled = true;
    progressDiv.classList.remove('hidden');

    // Simulate backup progress
    let progress = 0;
    const steps = [
        'Initializing backup...',
        'Backing up database...',
        'Backing up product images...',
        'Backing up static files...',
        'Compressing files...',
        'Finalizing backup...'
    ];

    const interval = setInterval(() => {
        progress += Math.random() * 20;
        if (progress > 100) progress = 100;

        progressBar.style.width = progress + '%';
        progressText.textContent = steps[Math.floor((progress / 100) * (steps.length - 1))];

        if (progress >= 100) {
            clearInterval(interval);

            // Call actual backup API
            fetch('/api/backup/create/', {
                method: 'POST',
                headers: {
                    'X-CSRFToken': getCookie('csrftoken'),
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                setTimeout(() => {
                    progressDiv.classList.add('hidden');
                    backupBtn.disabled = false;

                    if (data.success) {
                        showNotification('Backup created successfully!', 'success');
                        loadBackupStatus();
                        loadBackupList();
                    } else {
                        showNotification('Backup failed: ' + data.error, 'error');
                    }
                }, 1000);
            })
            .catch(error => {
                setTimeout(() => {
                    progressDiv.classList.add('hidden');
                    backupBtn.disabled = false;
                    showNotification('Backup failed: ' + error.message, 'error');
                }, 1000);
            });
        }
    }, 500);
}

function downloadBackup(filename) {
    showNotification(`Downloading backup: ${filename}`, 'info');

    // Create download link and trigger download
    const downloadUrl = `/api/backup/download/${filename}/`;
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

function restoreBackup(filename) {
    const confirmMessage = `⚠️ WARNING: This will replace ALL current data!\n\n` +
                          `Restore from backup: ${filename}\n\n` +
                          `This will:\n` +
                          `• Replace all products, categories, and sales\n` +
                          `• Overwrite the current database\n` +
                          `• Replace media files\n\n` +
                          `Current data will be PERMANENTLY LOST!\n\n` +
                          `Are you absolutely sure you want to continue?`;

    if (confirm(confirmMessage)) {
        showNotification(`Starting restore from: ${filename}`, 'warning');

        fetch(`/api/backup/restore/${filename}/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': getCookie('csrftoken'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(`System restored successfully! Please restart the POS system.`, 'success');

                // Show restart notice
                setTimeout(() => {
                    alert('Restore completed successfully!\n\nPlease restart the POS system to ensure all changes take effect.\n\nThe system will now reload...');
                    window.location.reload();
                }, 2000);
            } else {
                showNotification(`Restore failed: ${data.error}`, 'error');
            }
        })
        .catch(error => {
            showNotification(`Restore failed: ${error.message}`, 'error');
        });
    }
}

function deleteBackup(filename) {
    if (confirm(`Are you sure you want to delete backup: ${filename}?\n\nThis action cannot be undone!`)) {
        fetch(`/api/backup/delete/${filename}/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': getCookie('csrftoken'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(`Deleted backup: ${filename}`, 'success');
                loadBackupStatus();
                loadBackupList(); // Refresh the list
            } else {
                showNotification(`Error deleting backup: ${data.error}`, 'error');
            }
        })
        .catch(error => {
            showNotification(`Error deleting backup: ${error.message}`, 'error');
        });
    }
}

function refreshBackupList() {
    loadBackupStatus();
    loadBackupList();
    showNotification('Backup list refreshed', 'info');
}

function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

function setupAutoRefresh() {
    const autoRefreshCheckbox = document.getElementById('autoRefresh');
    
    function toggleAutoRefresh() {
        if (autoRefreshCheckbox.checked) {
            autoRefreshInterval = setInterval(() => {
                loadBackupStatus();
                loadBackupList();
            }, 30000); // Refresh every 30 seconds
        } else {
            clearInterval(autoRefreshInterval);
        }
    }
    
    autoRefreshCheckbox.addEventListener('change', toggleAutoRefresh);
    toggleAutoRefresh(); // Initialize
}

function showNotification(message, type) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
        type === 'success' ? 'bg-green-500' :
        type === 'warning' ? 'bg-yellow-500' :
        type === 'error' ? 'bg-red-500' : 'bg-blue-500'
    } text-white`;

    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas ${
                type === 'success' ? 'fa-check' :
                type === 'warning' ? 'fa-exclamation-triangle' :
                type === 'error' ? 'fa-times' : 'fa-info'
            } mr-2"></i>
            ${message}
        </div>
    `;

    document.body.appendChild(notification);

    // Remove after 3 seconds
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

function toggleMobileNav() {
    const mobileNav = document.getElementById('mobileNav');
    mobileNav.classList.toggle('hidden');
}
</script>
{% endblock %}
