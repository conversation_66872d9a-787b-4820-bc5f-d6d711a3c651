"""
Django management command to test POS system performance
Usage: python manage.py test_performance
"""

import time
import random
from django.core.management.base import BaseCommand
from django.db import connection
from django.test import Client
from django.contrib.auth.models import User
from pos.models import Category, Product, Sale, SaleItem
from pos.utils.performance import PerformanceMonitor, QueryCounter


class Command(BaseCommand):
    help = 'Test POS system performance with various scenarios'

    def add_arguments(self, parser):
        parser.add_argument(
            '--create-test-data',
            action='store_true',
            help='Create test data for performance testing',
        )
        parser.add_argument(
            '--test-products',
            type=int,
            default=1000,
            help='Number of test products to create',
        )
        parser.add_argument(
            '--test-sales',
            type=int,
            default=500,
            help='Number of test sales to create',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('🧪 Starting POS Performance Tests')
        )
        
        if options['create_test_data']:
            self.create_test_data(
                options['test_products'],
                options['test_sales']
            )
        
        # Run performance tests
        self.test_dashboard_performance()
        self.test_product_search_performance()
        self.test_sales_list_performance()
        self.test_api_performance()
        
        self.stdout.write(
            self.style.SUCCESS('✅ Performance testing completed!')
        )

    def create_test_data(self, num_products, num_sales):
        """Create test data for performance testing"""
        self.stdout.write('📊 Creating test data...')
        
        # Create categories
        categories = []
        category_names = [
            'Electronics', 'Clothing', 'Food & Beverages', 'Books',
            'Home & Garden', 'Sports', 'Toys', 'Health & Beauty',
            'Automotive', 'Office Supplies'
        ]
        
        for name in category_names:
            category, created = Category.objects.get_or_create(name=name)
            categories.append(category)
        
        # Create products
        self.stdout.write(f'Creating {num_products} test products...')
        products = []
        
        for i in range(num_products):
            product = Product.objects.create(
                name=f'Test Product {i+1}',
                barcode=f'TEST{i+1:06d}',
                description=f'Description for test product {i+1}',
                category=random.choice(categories),
                price=round(random.uniform(10.0, 500.0), 2),
                stock_quantity=random.randint(0, 100)
            )
            products.append(product)
            
            if (i + 1) % 100 == 0:
                self.stdout.write(f'  Created {i+1} products...')
        
        # Create test user
        user, created = User.objects.get_or_create(
            username='testuser',
            defaults={'email': '<EMAIL>'}
        )
        
        # Create sales
        self.stdout.write(f'Creating {num_sales} test sales...')
        
        for i in range(num_sales):
            sale = Sale.objects.create(
                cashier=user,
                subtotal=0,
                tax_amount=0,
                total_amount=0,
                payment_status=True,
                payment_reference=f'TEST{i+1:06d}'
            )
            
            # Add random items to sale
            num_items = random.randint(1, 5)
            for _ in range(num_items):
                product = random.choice(products)
                if product.stock_quantity > 0:
                    quantity = random.randint(1, min(3, product.stock_quantity))
                    SaleItem.objects.create(
                        sale=sale,
                        product=product,
                        quantity=quantity,
                        price_at_sale=product.price
                    )
            
            if (i + 1) % 50 == 0:
                self.stdout.write(f'  Created {i+1} sales...')
        
        self.stdout.write(self.style.SUCCESS('✅ Test data created'))

    def test_dashboard_performance(self):
        """Test dashboard loading performance"""
        self.stdout.write('\n🏠 Testing Dashboard Performance...')
        
        with PerformanceMonitor('Dashboard Load'):
            with QueryCounter() as counter:
                client = Client()
                
                # Create test user and login
                user = User.objects.first()
                if user:
                    client.force_login(user)
                    response = client.get('/dashboard/')
                    
                    if response.status_code == 200:
                        self.stdout.write(
                            f'  ✅ Dashboard loaded successfully'
                        )
                        self.stdout.write(
                            f'  📊 Queries executed: {counter.query_count}'
                        )
                    else:
                        self.stdout.write(
                            self.style.ERROR(f'  ❌ Dashboard failed: {response.status_code}')
                        )

    def test_product_search_performance(self):
        """Test product search performance"""
        self.stdout.write('\n🔍 Testing Product Search Performance...')
        
        search_terms = ['Test', 'Product', '001', 'Electronics']
        
        for term in search_terms:
            with PerformanceMonitor(f'Product Search: {term}'):
                with QueryCounter() as counter:
                    client = Client()
                    user = User.objects.first()
                    if user:
                        client.force_login(user)
                        response = client.get(f'/api/products/search/?search={term}&limit=20')
                        
                        if response.status_code == 200:
                            data = response.json()
                            self.stdout.write(
                                f'  ✅ Search "{term}": {len(data.get("products", []))} results'
                            )
                            self.stdout.write(
                                f'  📊 Queries: {counter.query_count}'
                            )

    def test_sales_list_performance(self):
        """Test sales list performance"""
        self.stdout.write('\n💰 Testing Sales List Performance...')
        
        with PerformanceMonitor('Sales List Load'):
            with QueryCounter() as counter:
                client = Client()
                user = User.objects.first()
                if user:
                    client.force_login(user)
                    response = client.get('/sales/')
                    
                    if response.status_code == 200:
                        self.stdout.write(
                            f'  ✅ Sales list loaded successfully'
                        )
                        self.stdout.write(
                            f'  📊 Queries executed: {counter.query_count}'
                        )

    def test_api_performance(self):
        """Test API endpoint performance"""
        self.stdout.write('\n🔌 Testing API Performance...')
        
        endpoints = [
            ('/api/products/load-more/?page=1&limit=20', 'Load More Products'),
            ('/api/categories/', 'Get Categories'),
        ]
        
        for endpoint, name in endpoints:
            with PerformanceMonitor(name):
                with QueryCounter() as counter:
                    client = Client()
                    user = User.objects.first()
                    if user:
                        client.force_login(user)
                        response = client.get(endpoint)
                        
                        if response.status_code == 200:
                            self.stdout.write(
                                f'  ✅ {name}: Success'
                            )
                            self.stdout.write(
                                f'  📊 Queries: {counter.query_count}'
                            )

    def analyze_database_performance(self):
        """Analyze current database performance"""
        self.stdout.write('\n📈 Database Performance Analysis...')
        
        with connection.cursor() as cursor:
            # Check table sizes
            tables = ['pos_product', 'pos_sale', 'pos_saleitem', 'pos_category']
            
            for table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                self.stdout.write(f'  {table}: {count:,} records')
            
            # Check index usage (SQLite specific)
            cursor.execute("""
                SELECT name, tbl_name 
                FROM sqlite_master 
                WHERE type='index' AND name NOT LIKE 'sqlite_%'
                ORDER BY tbl_name
            """)
            
            self.stdout.write('\n🗂️  Active Indexes:')
            for index_name, table_name in cursor.fetchall():
                self.stdout.write(f'  {table_name}: {index_name}')

    def benchmark_queries(self):
        """Benchmark common queries"""
        self.stdout.write('\n⚡ Query Benchmarks...')
        
        queries = [
            ("Product count", "SELECT COUNT(*) FROM pos_product"),
            ("Sales today", "SELECT COUNT(*) FROM pos_sale WHERE DATE(created_at) = DATE('now')"),
            ("Low stock products", "SELECT COUNT(*) FROM pos_product WHERE stock_quantity <= 10"),
            ("Recent sales", "SELECT * FROM pos_sale ORDER BY created_at DESC LIMIT 5"),
        ]
        
        for name, query in queries:
            start_time = time.time()
            
            with connection.cursor() as cursor:
                cursor.execute(query)
                result = cursor.fetchall()
            
            end_time = time.time()
            execution_time = (end_time - start_time) * 1000  # Convert to milliseconds
            
            self.stdout.write(
                f'  {name}: {execution_time:.2f}ms'
            )
