"""
Django management command for automated database backup
Usage: python manage.py backup_database
"""

import os
import json
import shutil
import zipfile
from datetime import datetime, timedelta
from pathlib import Path
from django.core.management.base import BaseCommand
from django.core import serializers
from django.conf import settings
from django.apps import apps
from pos.models import Category, Product, Sale, SaleItem

class Command(BaseCommand):
    help = 'Create automated backup of POS database and files'

    def add_arguments(self, parser):
        parser.add_argument(
            '--backup-dir',
            type=str,
            default='backups',
            help='Directory to store backups (default: backups)',
        )
        parser.add_argument(
            '--keep-days',
            type=int,
            default=30,
            help='Number of days to keep backups (default: 30)',
        )
        parser.add_argument(
            '--compress',
            action='store_true',
            help='Compress backup files',
        )

    def handle(self, *args, **options):
        self.backup_dir = options['backup_dir']
        self.keep_days = options['keep_days']
        self.compress = options['compress']
        
        self.stdout.write(self.style.SUCCESS('Starting POS Database Backup...'))
        self.stdout.write('=' * 50)
        
        # Create backup directory
        self.create_backup_directory()
        
        # Generate backup filename with timestamp
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_name = f'pos_backup_{timestamp}'
        backup_path = Path(self.backup_dir) / backup_name
        
        # Create backup
        self.create_backup(backup_path, timestamp)
        
        # Cleanup old backups
        self.cleanup_old_backups()
        
        self.stdout.write()
        self.stdout.write(self.style.SUCCESS('✅ Backup completed successfully!'))
        self.stdout.write(f'📁 Backup location: {backup_path}')

    def create_backup_directory(self):
        """Create backup directory if it doesn't exist"""
        backup_path = Path(self.backup_dir)
        backup_path.mkdir(exist_ok=True)
        self.stdout.write(f'📁 Backup directory: {backup_path.absolute()}')

    def create_backup(self, backup_path, timestamp):
        """Create complete backup of database and files"""
        backup_path.mkdir(exist_ok=True)
        
        # 1. Backup database data
        self.backup_database_data(backup_path)
        
        # 2. Backup database file (SQLite)
        self.backup_database_file(backup_path)
        
        # 3. Backup media files
        self.backup_media_files(backup_path)
        
        # 4. Backup static files
        self.backup_static_files(backup_path)
        
        # 5. Create backup info file
        self.create_backup_info(backup_path, timestamp)
        
        # 6. Compress if requested
        if self.compress:
            self.compress_backup(backup_path)

    def backup_database_data(self, backup_path):
        """Backup database data as JSON"""
        self.stdout.write('💾 Backing up database data...')
        
        data_dir = backup_path / 'data'
        data_dir.mkdir(exist_ok=True)
        
        # Backup each model
        models_to_backup = [
            ('categories', Category),
            ('products', Product),
            ('sales', Sale),
            ('sale_items', SaleItem),
        ]
        
        for name, model in models_to_backup:
            try:
                data = serializers.serialize('json', model.objects.all(), indent=2)
                file_path = data_dir / f'{name}.json'
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(data)
                
                count = model.objects.count()
                self.stdout.write(f'  ✅ {name}: {count} records')
                
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'  ❌ Error backing up {name}: {e}')
                )

    def backup_database_file(self, backup_path):
        """Backup SQLite database file"""
        self.stdout.write('🗄️  Backing up database file...')
        
        try:
            db_path = Path(settings.DATABASES['default']['NAME'])
            if db_path.exists():
                backup_db_path = backup_path / 'database'
                backup_db_path.mkdir(exist_ok=True)
                
                shutil.copy2(db_path, backup_db_path / 'db.sqlite3')
                self.stdout.write(f'  ✅ Database file: {db_path.name}')
            else:
                self.stdout.write('  ⚠️  Database file not found (may be in-memory)')
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'  ❌ Error backing up database file: {e}')
            )

    def backup_media_files(self, backup_path):
        """Backup media files"""
        self.stdout.write('📸 Backing up media files...')

        try:
            media_root = Path(settings.MEDIA_ROOT) if hasattr(settings, 'MEDIA_ROOT') and settings.MEDIA_ROOT else None

            if media_root and media_root.exists() and media_root != backup_path:
                backup_media_path = backup_path / 'media'

                # Avoid copying into itself
                if not str(media_root).startswith(str(backup_path)):
                    shutil.copytree(media_root, backup_media_path, dirs_exist_ok=True)

                    file_count = sum(1 for _ in backup_media_path.rglob('*') if _.is_file())
                    self.stdout.write(f'  ✅ Media files: {file_count} files')
                else:
                    self.stdout.write('  ⚠️  Skipping media backup (would cause recursion)')
            else:
                self.stdout.write('  ⚠️  Media directory not found or not configured')

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'  ❌ Error backing up media files: {e}')
            )

    def backup_static_files(self, backup_path):
        """Backup static files"""
        self.stdout.write('🎨 Backing up static files...')
        
        try:
            static_root = Path(settings.STATIC_ROOT) if hasattr(settings, 'STATIC_ROOT') and settings.STATIC_ROOT else None
            
            if static_root and static_root.exists():
                backup_static_path = backup_path / 'static'
                shutil.copytree(static_root, backup_static_path, dirs_exist_ok=True)
                
                file_count = sum(1 for _ in backup_static_path.rglob('*') if _.is_file())
                self.stdout.write(f'  ✅ Static files: {file_count} files')
            else:
                self.stdout.write('  ⚠️  Static files not collected or not found')
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'  ❌ Error backing up static files: {e}')
            )

    def create_backup_info(self, backup_path, timestamp):
        """Create backup information file"""
        self.stdout.write('📋 Creating backup info...')
        
        try:
            # Get database statistics
            stats = {
                'categories': Category.objects.count(),
                'products': Product.objects.count(),
                'sales': Sale.objects.count(),
                'sale_items': SaleItem.objects.count(),
            }
            
            backup_info = {
                'backup_date': datetime.now().isoformat(),
                'backup_timestamp': timestamp,
                'django_version': getattr(settings, 'DJANGO_VERSION', 'Unknown'),
                'database_stats': stats,
                'backup_type': 'automated_daily',
                'backup_size': self.get_directory_size(backup_path),
                'files_included': [
                    'Database data (JSON format)',
                    'SQLite database file',
                    'Media files',
                    'Static files (if collected)',
                ]
            }
            
            info_file = backup_path / 'backup_info.json'
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(backup_info, f, indent=2, ensure_ascii=False)
            
            self.stdout.write('  ✅ Backup info created')
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'  ❌ Error creating backup info: {e}')
            )

    def get_directory_size(self, path):
        """Get total size of directory in bytes"""
        try:
            total_size = sum(f.stat().st_size for f in Path(path).rglob('*') if f.is_file())
            return self.format_size(total_size)
        except:
            return 'Unknown'

    def format_size(self, size_bytes):
        """Format size in human readable format"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f} TB"

    def compress_backup(self, backup_path):
        """Compress backup directory"""
        self.stdout.write('🗜️  Compressing backup...')
        
        try:
            zip_path = f"{backup_path}.zip"
            
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for file_path in backup_path.rglob('*'):
                    if file_path.is_file():
                        arcname = file_path.relative_to(backup_path.parent)
                        zipf.write(file_path, arcname)
            
            # Remove uncompressed directory
            shutil.rmtree(backup_path)
            
            zip_size = Path(zip_path).stat().st_size
            self.stdout.write(f'  ✅ Compressed to: {self.format_size(zip_size)}')
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'  ❌ Error compressing backup: {e}')
            )

    def cleanup_old_backups(self):
        """Remove old backup files"""
        self.stdout.write(f'🧹 Cleaning up backups older than {self.keep_days} days...')
        
        try:
            backup_path = Path(self.backup_dir)
            cutoff_date = datetime.now() - timedelta(days=self.keep_days)
            
            removed_count = 0
            for item in backup_path.iterdir():
                if item.stat().st_mtime < cutoff_date.timestamp():
                    if item.is_dir():
                        shutil.rmtree(item)
                    else:
                        item.unlink()
                    removed_count += 1
            
            if removed_count > 0:
                self.stdout.write(f'  ✅ Removed {removed_count} old backup(s)')
            else:
                self.stdout.write('  ✅ No old backups to remove')
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'  ❌ Error cleaning up old backups: {e}')
            )
