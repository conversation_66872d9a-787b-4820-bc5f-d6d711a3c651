<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>{% block title %}POS System{% endblock %}</title>

    <!-- Tailwind CSS -->
        <script src="https://cdn.tailwindcss.com"></script>

    <!-- Google Fonts -->
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Heroicons -->
        <script src="https://unpkg.com/@heroicons/v2/24/solid"></script>
        <script src="https://unpkg.com/heroicons@2.0.18/24/outline/index.js"></script>
        <link href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" rel="stylesheet">


    <!-- Enhanced Navigation CSS -->
        {% load static %}
        <link rel="stylesheet" href="{% static 'css/enhanced_navigation.css' %}">

    <!-- Custom Styles -->
        <style>
            body {
                font-family: 'Inter', sans-serif;
            }
        </style>
        {% block extra_css %}{% endblock %}
    </head>
    <body class="bg-gray-50">
        <div class="min-h-screen flex flex-col">
        <!-- Main Content -->
            <div class="flex-1 flex flex-col overflow-hidden">
            <!-- SmartPOS Navigation Bar -->
                <header class="bg-gradient-to-r from-blue-600 to-blue-700 shadow-lg">
                    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div class="flex items-center justify-between h-16">
                            <div class="flex items-center space-x-8">
                                <!-- SmartPOS Brand -->
                                <div class="flex items-center space-x-2">
                                    <div class="bg-white p-2 rounded-lg">
                                        <i class="fas fa-cash-register text-blue-600 text-xl"></i>
                                    </div>
                                    <span class="text-white text-xl font-bold">SmartPOS</span>
                                </div>

                                <!-- Navigation Links -->
                                <nav class="hidden md:flex items-center space-x-3">
                                    <a href="{% url 'landing' %}" class="flex items-center space-x-2 text-white hover:text-blue-200 transition-colors duration-200 {% if request.resolver_match.url_name == 'landing' %}text-blue-200 bg-blue-800 bg-opacity-50 px-3 py-2 rounded-lg{% endif %}">
                                        <i class="fas fa-home"></i>
                                        <span class="font-medium">Home</span>
                                    </a>
                                    <a href="{% url 'dashboard' %}" class="flex items-center space-x-2 text-white hover:text-blue-200 transition-colors duration-200 {% if request.resolver_match.url_name == 'dashboard' %}text-blue-200 bg-blue-800 bg-opacity-50 px-3 py-2 rounded-lg{% endif %}">
                                        <i class="fas fa-tachometer-alt"></i>
                                        <span class="font-medium">Dashboard</span>
                                    </a>
                                    <a href="{% url 'create_sale' %}" class="flex items-center space-x-2 text-white hover:text-blue-200 transition-colors duration-200 {% if request.resolver_match.url_name == 'create_sale' %}text-blue-200 bg-blue-800 bg-opacity-50 px-3 py-2 rounded-lg{% endif %}">
                                        <i class="fas fa-shopping-cart"></i>
                                        <span class="font-medium">Point of Sale</span>
                                    </a>
                                    <a href="{% url 'products' %}" class="flex items-center space-x-2 text-white hover:text-blue-200 transition-colors duration-200 {% if request.resolver_match.url_name == 'products' %}text-blue-200 bg-blue-800 bg-opacity-50 px-3 py-2 rounded-lg{% endif %}">
                                        <i class="fas fa-box"></i>
                                        <span class="font-medium">Products</span>
                                    </a>
                                    <a href="{% url 'sales_report' %}" class="flex items-center space-x-2 text-white hover:text-blue-200 transition-colors duration-200 {% if request.resolver_match.url_name == 'sales_report' %}text-blue-200 bg-blue-800 bg-opacity-50 px-3 py-2 rounded-lg{% endif %}">
                                        <i class="fas fa-chart-bar"></i>
                                        <span class="font-medium">Reports</span>
                                    </a>
                                    <a href="{% url 'backup_dashboard' %}" class="flex items-center space-x-2 text-white hover:text-blue-200 transition-colors duration-200 {% if request.resolver_match.url_name == 'backup_dashboard' %}text-blue-200 bg-blue-800 bg-opacity-50 px-3 py-2 rounded-lg{% endif %}">
                                        <i class="fas fa-shield-alt"></i>
                                        <span class="font-medium">Backup</span>
                                    </a>
                                </nav>
                            </div>

                            <!-- Right side - User menu and mobile button -->
                            <div class="flex items-center space-x-4">
                                {% if user.is_authenticated %}
                                    <div class="relative">
                                        <button type="button" onclick="toggleDropdown()" class="flex items-center space-x-2 text-white hover:text-blue-200 transition-colors duration-200 bg-blue-800 bg-opacity-50 px-3 py-2 rounded-lg">
                                            <span class="font-medium">{{ user.username }}</span>
                                            <i class="fas fa-user-circle text-xl"></i>
                                        </button>
                                        <div id="userDropdown" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                                            <div class="py-1">
                                                <div class="px-4 py-2 text-sm text-gray-500 border-b bg-gray-50">
                                                    <div class="flex items-center">
                                                        <i class="fas fa-user-shield text-blue-600 mr-2"></i>
                                                        Owner Account
                                                    </div>
                                                </div>
                                                <a href="{% url 'dashboard' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center">
                                                    <i class="fas fa-tachometer-alt mr-2"></i>
                                                    Dashboard
                                                </a>
                                                <a href="{% url 'landing' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center">
                                                    <i class="fas fa-home mr-2"></i>
                                                    Home
                                                </a>
                                                <div class="border-t">
                                                    <a href="{% url 'logout' %}" class="block px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center">
                                                        <i class="fas fa-sign-out-alt mr-2"></i>
                                                        Logout
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                {% else %}
                                    <a href="{% url 'login' %}" class="bg-white text-blue-600 px-4 py-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 flex items-center font-medium">
                                        <i class="fas fa-sign-in-alt mr-2"></i>
                                        Login
                                    </a>
                                {% endif %}

                                <!-- Mobile Menu Button -->
                                <button id="mobileMenuBtn" class="md:hidden text-white hover:text-blue-200 transition-colors duration-200">
                                    <i class="fas fa-bars text-xl"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Mobile Navigation -->
                        <div id="mobileNav" class="hidden md:hidden border-t border-blue-500 py-4">
                            <nav class="flex flex-col space-y-3">
                                <a href="{% url 'landing' %}" class="flex items-center space-x-2 text-white hover:text-blue-200 transition-colors duration-200 {% if request.resolver_match.url_name == 'landing' %}text-blue-200 bg-blue-800 bg-opacity-50 px-3 py-2 rounded-lg{% endif %}">
                                    <i class="fas fa-home"></i>
                                    <span class="font-medium">Home</span>
                                </a>
                                <a href="{% url 'dashboard' %}" class="flex items-center space-x-2 text-white hover:text-blue-200 transition-colors duration-200 {% if request.resolver_match.url_name == 'dashboard' %}text-blue-200 bg-blue-800 bg-opacity-50 px-3 py-2 rounded-lg{% endif %}">
                                    <i class="fas fa-tachometer-alt"></i>
                                    <span class="font-medium">Dashboard</span>
                                </a>
                                <a href="{% url 'create_sale' %}" class="flex items-center space-x-2 text-white hover:text-blue-200 transition-colors duration-200 {% if request.resolver_match.url_name == 'create_sale' %}text-blue-200 bg-blue-800 bg-opacity-50 px-3 py-2 rounded-lg{% endif %}">
                                    <i class="fas fa-shopping-cart"></i>
                                    <span class="font-medium">Point of Sale</span>
                                </a>
                                <a href="{% url 'products' %}" class="flex items-center space-x-2 text-white hover:text-blue-200 transition-colors duration-200 {% if request.resolver_match.url_name == 'products' %}text-blue-200 bg-blue-800 bg-opacity-50 px-3 py-2 rounded-lg{% endif %}">
                                    <i class="fas fa-box"></i>
                                    <span class="font-medium">Products</span>
                                </a>
                                <a href="{% url 'sales_report' %}" class="flex items-center space-x-2 text-white hover:text-blue-200 transition-colors duration-200 {% if request.resolver_match.url_name == 'sales_report' %}text-blue-200 bg-blue-800 bg-opacity-50 px-3 py-2 rounded-lg{% endif %}">
                                    <i class="fas fa-chart-bar"></i>
                                    <span class="font-medium">Reports</span>
                                </a>
                                <a href="{% url 'backup_dashboard' %}" class="flex items-center space-x-2 text-white hover:text-blue-200 transition-colors duration-200 {% if request.resolver_match.url_name == 'backup_dashboard' %}text-blue-200 bg-blue-800 bg-opacity-50 px-3 py-2 rounded-lg{% endif %}">
                                    <i class="fas fa-shield-alt"></i>
                                    <span class="font-medium">Backup</span>
                                </a>
                            </nav>
                        </div>
                    </div>

                    <script>
                        function toggleDropdown() {
                            const dropdown = document.getElementById('userDropdown');
                            dropdown.classList.toggle('hidden');
                        }

                        // Mobile menu toggle functionality
                        document.addEventListener('DOMContentLoaded', function() {
                            const mobileMenuBtn = document.getElementById('mobileMenuBtn');
                            const mobileNav = document.getElementById('mobileNav');

                            if (mobileMenuBtn && mobileNav) {
                                mobileMenuBtn.addEventListener('click', function() {
                                    mobileNav.classList.toggle('hidden');
                                });
                            }
                        });

                        // Close dropdown when clicking outside
                        window.onclick = function(event) {
                            if (!event.target.closest('.relative')) {
                                var dropdown = document.getElementById('userDropdown');
                                if (dropdown && !dropdown.classList.contains('hidden')) {
                                    dropdown.classList.add('hidden');
                                }
                            }
                        }
                    </script>
                </header>            <!-- Main Content Area -->
                <main class="flex-1 overflow-y-auto bg-gray-50">
                    <div class="py-6">
                        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                            {% block content %}{% endblock %}
                        </div>
                    </div>
                </main>
            </div>
        </div>

        {% block extra_js %}{% endblock %}
    </body>
</html>


